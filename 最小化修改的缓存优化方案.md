# 最小化修改的缓存优化方案

## 🎯 核心需求分析

根据您的需求，我们需要解决以下核心问题：

### 主要目标
1. **智能缓存复用**：支持意外中断后的文件复用，避免重复下载
2. **版本识别机制**：正确识别解压出的APK文件版本，确保安装正确版本
3. **存储空间管理**：通过版本更新时清理旧版本，避免无限累积
4. **最新版本清理**：已是最新版本时清理不需要的缓存APK文件

### 技术挑战
- ZIP文件下载后解压出APK，需要建立ZIP版本与APK版本的对应关系
- 意外中断后需要识别本地APK是否为当前需要的版本
- 最小化代码修改，不影响现有功能

## 🔧 最小化修改方案

### 1. 核心思路

#### 1.1 文件命名策略优化
```kotlin
// 当前命名：app-1.0.1.apk
// 优化命名：app-1.0.1-{md5前8位}.apk
// 这样可以通过文件名直接识别版本和内容
```

#### 1.2 缓存检测增强
在下载前检查是否存在匹配的APK文件，匹配条件：
- 版本名称相同
- MD5匹配（通过文件名中的MD5片段快速预检）

#### 1.3 智能清理策略
- 在下载新版本前，清理旧版本APK文件
- 在已是最新版本时，清理不需要的缓存APK文件

### 2. 具体实现方案

#### 2.1 修改DownloadManager - 增强缓存检测

```kotlin
// 在downloadApk方法开始处添加增强缓存检测
private suspend fun checkVersionCache(context: Context, updateInfo: UpdateInfo): File? {
    return withContext(Dispatchers.IO) {
        try {
            val updateDir = UpdateUtils.getUpdateCacheDir(context)
            val files = updateDir.listFiles() ?: return@withContext null
            
            // 查找匹配的APK文件
            // 文件名格式：app-{version}-{md5前8位}.apk
            val md5Prefix = updateInfo.fileMd5.take(8)
            val expectedFileName = "app-${updateInfo.versionName}-$md5Prefix.apk"
            
            val cachedApk = files.find { file ->
                file.name == expectedFileName && file.exists() && file.length() > 0
            }
            
            if (cachedApk != null) {
                Log.i(TAG, "找到缓存的APK文件: ${cachedApk.name}")
                
                // 验证文件完整性（可选，快速检查）
                if (cachedApk.length() > 1024 * 1024) { // 至少1MB，基本完整性检查
                    _downloadProgress.value = DownloadProgress(
                        status = DownloadStatus.COMPLETED,
                        progress = 100,
                        downloadedBytes = cachedApk.length(),
                        totalBytes = updateInfo.fileSize,
                        speed = "使用本地缓存",
                        remainingTime = "缓存文件"
                    )
                    
                    return@withContext cachedApk
                }
            }
            
            return@withContext null
            
        } catch (e: Exception) {
            Log.e(TAG, "缓存检测失败", e)
            return@withContext null
        }
    }
}

// 在downloadApk方法开始处调用
suspend fun downloadApk(context: Context, updateInfo: UpdateInfo): File? {
    return withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "开始下载APK: ${updateInfo.versionName}")
            
            // 1. 首先检查缓存
            val cachedFile = checkVersionCache(context, updateInfo)
            if (cachedFile != null) {
                return@withContext cachedFile
            }
            
            // 2. 继续原有下载逻辑...
            // （保持现有代码不变）
```

#### 2.2 修改APK文件命名策略

```kotlin
// 在extractApkFromZip方法中修改APK文件命名
private suspend fun extractApkFromZip(zipFile: File, updateInfo: UpdateInfo): File? {
    // ... 现有解压逻辑保持不变 ...
    
    when (val result = ZipExtractor.extractApkFromZip(zipFile, extractDir, progressCallback)) {
        is ZipExtractor.ExtractResult.Success -> {
            Log.i(TAG, "ZIP解压成功，APK文件: ${result.apkFile.name}")
            
            // 修改：使用包含MD5信息的文件名
            val md5Prefix = updateInfo.fileMd5.take(8)
            val finalApkFile = File(zipFile.parent, "app-${updateInfo.versionName}-$md5Prefix.apk")
            
            // 使用复制而不是移动，更安全可靠
            try {
                result.apkFile.copyTo(finalApkFile, overwrite = true)
                Log.i(TAG, "APK文件复制成功: ${finalApkFile.name}")
                
                // 验证复制结果
                if (finalApkFile.exists() && finalApkFile.length() == result.apkFile.length()) {
                    // 保存清理信息，供安装完成后使用
                    saveCleanupInfo(zipFile, extractDir)
                    
                    Log.i(TAG, "APK文件准备完成，等待安装完成后清理临时文件")
                    return@extractApkFromZip finalApkFile
                } else {
                    Log.e(TAG, "文件复制验证失败: 目标文件不存在或大小不匹配")
                    finalApkFile.delete()
                    ZipExtractor.cleanupExtractedFiles(extractDir)
                    return@extractApkFromZip null
                }
            } catch (e: Exception) {
                Log.e(TAG, "APK文件复制失败", e)
                ZipExtractor.cleanupExtractedFiles(extractDir)
                return@extractApkFromZip null
            }
        }
        // ... 其他情况保持不变 ...
    }
}
```

#### 2.3 修改下载前的清理策略

```kotlin
// 在DownloadManager的downloadApk方法中，下载前清理旧版本
suspend fun downloadApk(context: Context, updateInfo: UpdateInfo): File? {
    return withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "开始下载APK: ${updateInfo.versionName}")

            // 1. 首先检查缓存
            val cachedFile = checkVersionCache(context, updateInfo)
            if (cachedFile != null) {
                return@withContext cachedFile
            }

            // 2. 清理旧版本APK文件（在下载新版本前）
            UpdateUtils.cleanupOldVersionApks(context, updateInfo.versionName, updateInfo.fileMd5)

            // 3. 继续原有下载逻辑...
            // （保持现有代码不变）
```

```kotlin
// 在UpdateManager中保持原有逻辑，不添加安装后清理
when (installResult) {
    is InstallResult.Success -> {
        Log.i(TAG, "安装启动成功")
        _updateState.value = UpdateState.InstallStarted(info, apkFile)

        // 安装启动成功后，只执行延迟清理（ZIP和解压目录）
        downloadManager.performDelayedCleanup()

        // 不再清理APK文件，让其保留用于可能的重新安装
    }
}
```

#### 2.4 添加缓存清理工具方法

```kotlin
// 在UpdateUtils中添加清理APK文件的方法
/**
 * 清理旧版本的APK文件，保留当前版本
 */
fun cleanupOldVersionApks(context: Context, currentVersionName: String, currentMd5: String) {
    try {
        val updateDir = getUpdateCacheDir(context)
        val files = updateDir.listFiles() ?: return

        val currentMd5Prefix = currentMd5.take(8)
        val currentFileName = "app-$currentVersionName-$currentMd5Prefix.apk"

        files.filter { file ->
            file.name.endsWith(".apk") &&
            file.name.startsWith("app-") &&
            file.name != currentFileName
        }.forEach { file ->
            try {
                file.delete()
                Log.d(TAG, "清理旧版本APK: ${file.name}")
            } catch (e: Exception) {
                Log.e(TAG, "删除文件失败: ${file.name}", e)
            }
        }

    } catch (e: Exception) {
        Log.e(TAG, "清理旧版本APK失败", e)
    }
}

/**
 * 清理当前版本的缓存APK文件（已是最新版本时使用）
 */
fun cleanupCurrentVersionApk(context: Context, currentVersionName: String) {
    try {
        val updateDir = getUpdateCacheDir(context)
        val files = updateDir.listFiles() ?: return

        files.filter { file ->
            file.name.endsWith(".apk") &&
            file.name.startsWith("app-$currentVersionName-")
        }.forEach { file ->
            try {
                file.delete()
                Log.d(TAG, "清理当前版本缓存APK: ${file.name}")
            } catch (e: Exception) {
                Log.e(TAG, "删除文件失败: ${file.name}", e)
            }
        }

    } catch (e: Exception) {
        Log.e(TAG, "清理当前版本缓存APK失败", e)
    }
}

/**
 * 检查是否有缓存的APK文件
 */
fun hasCachedApkFiles(context: Context): Boolean {
    return try {
        val updateDir = getUpdateCacheDir(context)
        val files = updateDir.listFiles() ?: return false

        files.any { file ->
            file.name.endsWith(".apk") && file.name.startsWith("app-")
        }
    } catch (e: Exception) {
        Log.e(TAG, "检查缓存APK文件失败", e)
        false
    }
}
```

### 3. 使用场景验证

#### 3.1 正常下载安装流程
```
1. 检查更新 → 发现新版本1.0.2
2. 检查缓存 → 无匹配缓存
3. 清理旧版本 → 删除旧版本APK文件（如果存在）
4. 下载ZIP → app-1.0.2.zip
5. 解压APK → app-1.0.2-bd13ebc1.apk
6. 启动安装 → 安装界面显示
7. 延迟清理 → 删除ZIP和解压目录
8. APK文件保留 → 用于可能的重新安装
```

#### 3.2 意外中断后恢复流程
```
1. 检查更新 → 发现新版本1.0.2（与上次相同）
2. 检查缓存 → 找到app-1.0.2-bd13ebc1.apk
3. 跳过下载 → 直接使用缓存文件
4. 启动安装 → 安装界面显示
5. APK文件保留 → 继续可用于重新安装
```

#### 3.3 版本变更流程
```
1. 检查更新 → 发现新版本1.0.3
2. 检查缓存 → 无匹配缓存（版本号不同）
3. 清理旧版本 → 删除app-1.0.2-bd13ebc1.apk
4. 下载新版本 → 正常下载流程
5. 新APK保留 → app-1.0.3-xyz12345.apk
```

#### 3.4 已是最新版本清理流程
```
1. 检查更新 → 已是最新版本1.0.2
2. 检查缓存 → 发现app-1.0.2-bd13ebc1.apk
3. 清理缓存 → 删除不需要的APK文件
4. 释放空间 → 存储空间得到释放
```

## 📝 代码修改清单

### 需要修改的文件
1. **DownloadManager.kt**
   - 添加`checkVersionCache`方法
   - 在`downloadApk`开始处调用缓存检测和旧版本清理
   - 修改`extractApkFromZip`中的文件命名

2. **UpdateUtils.kt**
   - 添加`cleanupOldVersionApks`方法
   - 添加`cleanupCurrentVersionApk`方法
   - 添加`hasCachedApkFiles`方法

3. **UpdateManager.kt**（可选）
   - 在无更新时检查并清理缓存APK文件

### 修改量评估
- **新增代码**：约60行
- **修改代码**：约10行
- **总计变更**：约70行
- **影响范围**：最小化，不影响现有功能

## ✅ 方案优势

### 1. 最小化修改
- 基于现有代码扩展，修改量极小
- 不影响现有的ZIP解压安装功能
- 向后兼容，平滑升级

### 2. 智能缓存管理
- 通过文件名快速识别版本和内容
- 支持意外中断后的文件复用
- 版本更新时清理旧版本，避免无限累积

### 3. 用户体验优化
- 意外中断后无需重新下载
- APK文件保留支持重复安装
- 快速安装提升用户体验

### 4. 技术可靠性
- 文件完整性基础验证
- 版本匹配准确识别
- 异常处理保证稳定性

## 🎯 实施建议

### 实施步骤
1. **第1天**：实现缓存检测逻辑
2. **第2天**：修改文件命名策略
3. **第3天**：实现版本更新时清理
4. **第4天**：测试验证功能
5. **第5天**：代码优化和文档

### 测试重点
- 正常下载安装流程
- 意外中断后恢复
- 版本变更时的处理
- 旧版本清理验证
- APK文件安全保留

## 🔒 安全性保障

### 1. 文件安全
- **安装期间保护**：APK文件在安装期间不会被清理
- **重复安装支持**：用户可以多次尝试安装同一APK
- **版本隔离**：不同版本的APK文件独立管理

### 2. 存储管理
- **按需清理**：只在下载新版本时清理旧版本
- **空间控制**：避免APK文件无限累积
- **用户可控**：用户可以手动管理缓存文件

### 3. 异常处理
- **清理失败容错**：清理失败不影响下载和安装
- **文件损坏检测**：基础的文件完整性验证
- **版本匹配保证**：严格的版本和MD5匹配

## 💡 最新版本清理功能实现

### 在UpdateManager中添加无更新时的清理逻辑

```kotlin
// 在UpdateManager的checkForUpdate方法中添加
suspend fun checkForUpdate(context: Context): UpdateCheckResult {
    return withContext(Dispatchers.IO) {
        try {
            // ... 现有更新检查逻辑 ...

            val result = updateRepository.checkForUpdate()

            when (result) {
                is UpdateCheckResult.NoUpdate -> {
                    // 已是最新版本，检查是否有缓存的APK文件需要清理
                    if (UpdateUtils.hasCachedApkFiles(context)) {
                        val currentVersion = getCurrentVersionName(context)
                        UpdateUtils.cleanupCurrentVersionApk(context, currentVersion)
                        Log.i(TAG, "已是最新版本，清理缓存APK文件")
                    }
                    return@withContext result
                }
                is UpdateCheckResult.HasUpdate -> {
                    // 有更新时的现有逻辑保持不变
                    return@withContext result
                }
                else -> {
                    return@withContext result
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "检查更新失败", e)
            return@withContext UpdateCheckResult.NetworkError
        }
    }
}

/**
 * 获取当前应用版本名称
 */
private fun getCurrentVersionName(context: Context): String {
    return try {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
        packageInfo.versionName ?: "unknown"
    } catch (e: Exception) {
        Log.e(TAG, "获取当前版本名称失败", e)
        "unknown"
    }
}
```

### 用户友好的清理提示（可选）

```kotlin
// 可以在UI层添加清理提示
when (val result = updateManager.checkForUpdate(context)) {
    is UpdateCheckResult.NoUpdate -> {
        if (UpdateUtils.hasCachedApkFiles(context)) {
            // 显示清理提示
            showMessage("已是最新版本，已清理缓存文件释放存储空间")
        } else {
            showMessage("已是最新版本")
        }
    }
    // ... 其他情况处理
}
```

这个优化方案不仅去掉了不安全的安装后自动清理策略，还增加了在已是最新版本时的智能清理功能，既支持了文件复用，又避免了安装过程中的文件安全风险，同时还能在适当的时机释放存储空间，是一个更加完善和可靠的解决方案。
