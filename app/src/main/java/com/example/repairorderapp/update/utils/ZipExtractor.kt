package com.example.repairorderapp.update.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.*
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream

/**
 * ZIP文件解压工具类
 * 专门用于处理更新包的ZIP文件解压和APK提取
 */
object ZipExtractor {
    
    private const val TAG = "ZipExtractor"
    private const val BUFFER_SIZE = 8192
    
    /**
     * 解压结果
     */
    sealed class ExtractResult {
        data class Success(val apkFile: File) : ExtractResult()
        data class Failed(val message: String) : ExtractResult()
        object NoApkFound : ExtractResult()
    }
    
    /**
     * 解压进度回调
     */
    interface ProgressCallback {
        fun onProgress(progress: Int, currentFile: String)
        fun onError(message: String)
    }
    
    /**
     * 检查文件是否为ZIP格式
     * @param file 要检查的文件
     * @return 是否为ZIP文件
     */
    fun isZipFile(file: File): <PERSON>olean {
        return try {
            if (!file.exists() || file.length() < 4) {
                return false
            }
            
            // 检查ZIP文件头标识 (PK)
            FileInputStream(file).use { fis ->
                val header = ByteArray(4)
                fis.read(header)
                // ZIP文件头：50 4B 03 04 或 50 4B 05 06 或 50 4B 07 08
                header[0] == 0x50.toByte() && header[1] == 0x4B.toByte()
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查ZIP文件格式失败", e)
            false
        }
    }
    
    /**
     * 从文件名判断是否为ZIP文件
     * @param fileName 文件名
     * @return 是否为ZIP文件
     */
    fun isZipFile(fileName: String): Boolean {
        return fileName.lowercase().endsWith(".zip")
    }
    
    /**
     * 解压ZIP文件并提取APK
     * @param zipFile ZIP文件
     * @param extractDir 解压目录
     * @param progressCallback 进度回调
     * @return 解压结果
     */
    suspend fun extractApkFromZip(
        zipFile: File,
        extractDir: File,
        progressCallback: ProgressCallback? = null
    ): ExtractResult = withContext(Dispatchers.IO) {
        
        try {
            Log.i(TAG, "开始解压ZIP文件: ${zipFile.name}")
            
            if (!zipFile.exists()) {
                return@withContext ExtractResult.Failed("ZIP文件不存在")
            }
            
            if (!isZipFile(zipFile)) {
                return@withContext ExtractResult.Failed("文件不是有效的ZIP格式")
            }
            
            // 创建解压目录
            if (!extractDir.exists()) {
                extractDir.mkdirs()
            }
            
            var apkFile: File? = null
            var totalEntries = 0
            var processedEntries = 0
            
            // 第一遍：计算总条目数
            ZipInputStream(FileInputStream(zipFile)).use { zis ->
                while (zis.nextEntry != null) {
                    totalEntries++
                    zis.closeEntry()
                }
            }
            

            
            // 第二遍：实际解压
            ZipInputStream(FileInputStream(zipFile)).use { zis ->
                var entry: ZipEntry?
                
                while (zis.nextEntry.also { entry = it } != null) {
                    val zipEntry = entry!!
                    processedEntries++
                    
                    // 计算进度 (0-90%用于解压，90-100%用于验证)
                    val progress = (processedEntries * 90) / totalEntries
                    progressCallback?.onProgress(progress, zipEntry.name)
                    
                    // 安全检查：防止目录遍历攻击
                    if (zipEntry.name.contains("..") || zipEntry.name.startsWith("/")) {
                        Log.w(TAG, "跳过不安全的路径: ${zipEntry.name}")
                        zis.closeEntry()
                        continue
                    }
                    
                    val outputFile = File(extractDir, zipEntry.name)
                    
                    if (zipEntry.isDirectory) {
                        // 创建目录
                        outputFile.mkdirs()
                        Log.d(TAG, "创建目录: ${outputFile.name}")
                    } else {
                        // 解压文件
                        outputFile.parentFile?.mkdirs()
                        
                        FileOutputStream(outputFile).use { fos ->
                            val buffer = ByteArray(BUFFER_SIZE)
                            var bytesRead: Int
                            while (zis.read(buffer).also { bytesRead = it } != -1) {
                                fos.write(buffer, 0, bytesRead)
                            }
                        }
                        
                        // 检查是否为APK文件
                        if (outputFile.name.lowercase().endsWith(".apk")) {
                            if (apkFile == null || isMainApk(outputFile)) {
                                apkFile = outputFile
                                Log.i(TAG, "找到APK文件: ${outputFile.name}")
                            }
                        }
                    }
                    
                    zis.closeEntry()
                }
            }
            
            // 验证APK文件
            progressCallback?.onProgress(95, "验证APK文件...")

            val finalApkFile = apkFile
            if (finalApkFile == null) {
                Log.e(TAG, "ZIP文件中未找到APK文件")
                return@withContext ExtractResult.NoApkFound
            }

            if (!finalApkFile.exists() || finalApkFile.length() == 0L) {
                Log.e(TAG, "APK文件无效: ${finalApkFile.name}")
                return@withContext ExtractResult.Failed("APK文件无效")
            }

            progressCallback?.onProgress(100, "解压完成")
            Log.i(TAG, "ZIP解压成功，APK文件: ${finalApkFile.name} (${finalApkFile.length()} bytes)")

            return@withContext ExtractResult.Success(finalApkFile)
            
        } catch (e: Exception) {
            Log.e(TAG, "解压ZIP文件失败", e)
            progressCallback?.onError("解压失败: ${e.message}")
            return@withContext ExtractResult.Failed("解压失败: ${e.message}")
        }
    }
    
    /**
     * 判断是否为主APK文件
     * 优先选择文件名不包含特殊标识的APK
     */
    private fun isMainApk(apkFile: File): Boolean {
        val fileName = apkFile.name.lowercase()
        
        // 排除一些常见的非主APK文件
        val excludePatterns = listOf(
            "test", "debug", "sample", "demo", 
            "split", "config", "base"
        )
        
        return excludePatterns.none { fileName.contains(it) }
    }
    
    /**
     * 清理解压的临时文件
     * @param extractDir 解压目录
     */
    fun cleanupExtractedFiles(extractDir: File) {
        try {
            if (extractDir.exists() && extractDir.isDirectory) {
                extractDir.deleteRecursively()
            }
        } catch (e: Exception) {
            Log.e(TAG, "清理解压目录失败", e)
        }
    }
    
    /**
     * 获取ZIP文件中的APK文件列表（不解压）
     * @param zipFile ZIP文件
     * @return APK文件名列表
     */
    fun getApkListFromZip(zipFile: File): List<String> {
        val apkList = mutableListOf<String>()
        
        try {
            ZipInputStream(FileInputStream(zipFile)).use { zis ->
                var entry: ZipEntry?
                
                while (zis.nextEntry.also { entry = it } != null) {
                    val zipEntry = entry!!
                    
                    if (!zipEntry.isDirectory && zipEntry.name.lowercase().endsWith(".apk")) {
                        apkList.add(zipEntry.name)
                    }
                    
                    zis.closeEntry()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "读取ZIP文件APK列表失败", e)
        }
        
        return apkList
    }
}
