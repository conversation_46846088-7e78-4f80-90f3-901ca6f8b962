package com.example.repairorderapp.update.manager

import android.content.Context
import android.util.Log
import com.example.repairorderapp.update.model.*
import com.example.repairorderapp.update.repository.UpdateRepository
import com.example.repairorderapp.update.utils.ApkInstaller
import com.example.repairorderapp.update.utils.FileVerifier
import com.example.repairorderapp.update.utils.UpdateUtils
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

/**
 * 更新管理器 - 核心类
 * 统一管理应用更新的整个流程：检查更新 -> 下载 -> 安装
 */
class UpdateManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "UpdateManager"
        
        @Volatile
        private var INSTANCE: UpdateManager? = null
        
        fun getInstance(context: Context): UpdateManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UpdateManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val updateRepository = UpdateRepository(context)
    private val downloadManager = DownloadManager(context)
    
    private val _updateState = MutableStateFlow<UpdateState>(UpdateState.Idle)
    val updateState: StateFlow<UpdateState> = _updateState.asStateFlow()
    
    // 当前更新信息
    private var currentUpdateInfo: UpdateInfo? = null
    
    /**
     * 检查应用更新
     * @param showNoUpdateMessage 是否显示"已是最新版本"的消息
     * @return 更新结果
     */
    suspend fun checkUpdate(showNoUpdateMessage: Boolean = false): UpdateResult {
        return try {
            _updateState.value = UpdateState.Checking
            Log.d(TAG, "开始检查更新...")
            
            // 检查网络连接
            if (!updateRepository.isNetworkAvailable()) {
                val errorMsg = "网络连接不可用"
                Log.w(TAG, errorMsg)
                _updateState.value = UpdateState.Error(errorMsg)
                return UpdateResult.Error(errorMsg)
            }
            
            // 调用API检查更新
            val result = updateRepository.checkUpdate()
            
            when (result) {
                is UpdateResult.HasUpdate -> {
                    currentUpdateInfo = result.updateInfo
                    _updateState.value = UpdateState.UpdateAvailable(result.updateInfo)
                    Log.i(TAG, "发现新版本: ${result.updateInfo.versionName}")
                }
                is UpdateResult.NoUpdate -> {
                    _updateState.value = if (showNoUpdateMessage) {
                        UpdateState.NoUpdate
                    } else {
                        UpdateState.Idle
                    }
                    Log.d(TAG, "当前已是最新版本")
                }
                is UpdateResult.Error -> {
                    _updateState.value = UpdateState.Error(result.message)
                    Log.e(TAG, "检查更新失败: ${result.message}")
                }
            }
            
            result
            
        } catch (e: Exception) {
            val errorMsg = "检查更新异常: ${e.message}"
            Log.e(TAG, errorMsg, e)
            _updateState.value = UpdateState.Error(errorMsg)
            UpdateResult.Error(errorMsg, e)
        }
    }
    
    /**
     * 开始下载更新
     * @param updateInfo 更新信息，如果为null则使用当前的更新信息
     */
    suspend fun startDownload(updateInfo: UpdateInfo? = null): File? {
        val info = updateInfo ?: currentUpdateInfo
        if (info == null) {
            Log.e(TAG, "没有可用的更新信息")
            _updateState.value = UpdateState.Error("没有可用的更新信息")
            return null
        }
        
        return try {
            _updateState.value = UpdateState.Downloading(info)
            Log.i(TAG, "开始下载更新: ${info.versionName}")
            
            // 检查存储空间
            val availableSpace = getAvailableStorageSpace()
            if (availableSpace < info.fileSize * 2) { // 预留双倍空间
                val errorMsg = "存储空间不足，需要 ${UpdateUtils.formatBytes(info.fileSize * 2)}"
                Log.w(TAG, errorMsg)
                _updateState.value = UpdateState.Error(errorMsg)
                return null
            }
            
            // 执行下载
            val downloadedFile = downloadManager.downloadApk(info)
            
            if (downloadedFile != null) {
                _updateState.value = UpdateState.DownloadCompleted(info, downloadedFile)
                Log.i(TAG, "下载完成: ${downloadedFile.name}")
            } else {
                _updateState.value = UpdateState.Error("下载失败")
                Log.e(TAG, "下载失败")
            }
            
            downloadedFile
            
        } catch (e: CancellationException) {
            Log.i(TAG, "下载被取消")
            _updateState.value = UpdateState.Cancelled
            throw e
        } catch (e: Exception) {
            val errorMsg = "下载异常: ${e.message}"
            Log.e(TAG, errorMsg, e)
            _updateState.value = UpdateState.Error(errorMsg)
            null
        }
    }
    
    /**
     * 安装APK
     * @param apkFile APK文件
     * @param updateInfo 更新信息
     */
    fun installApk(apkFile: File, updateInfo: UpdateInfo? = null): InstallResult {
        return try {
            val info = updateInfo ?: currentUpdateInfo
            if (info == null) {
                Log.e(TAG, "没有更新信息，无法验证APK")
                return InstallResult.Failed("没有更新信息")
            }
            
            Log.i(TAG, "开始安装APK: ${apkFile.name}")
            _updateState.value = UpdateState.Installing(info, apkFile)

            // 验证APK文件
            // 注意：如果是从ZIP解压出来的APK，MD5校验应该跳过，因为info.fileMd5是ZIP文件的MD5
            val shouldSkipMd5 = info.downloadUrl.lowercase().endsWith(".zip")
            val verificationResult = if (shouldSkipMd5) {
                FileVerifier.verifyApkFile(context, apkFile, null) // 跳过MD5校验
            } else {
                FileVerifier.verifyApkFile(context, apkFile, info.fileMd5)
            }
            when (verificationResult) {
                is FileVerifier.VerificationResult.Failed -> {
                    Log.e(TAG, "APK验证失败: ${verificationResult.reason}")
                    _updateState.value = UpdateState.Error("APK验证失败: ${verificationResult.reason}")
                    return InstallResult.Failed(verificationResult.reason)
                }
                is FileVerifier.VerificationResult.Success -> {
                    Log.i(TAG, "APK验证成功")
                }
            }
            
            // 执行安装
            val installResult = ApkInstaller.installApk(context, apkFile)
            
            when (installResult) {
                is InstallResult.Success -> {
                    Log.i(TAG, "安装启动成功")
                    _updateState.value = UpdateState.InstallStarted(info, apkFile)

                    // 安装启动成功后，执行延迟清理
                    downloadManager.performDelayedCleanup()
                }
                is InstallResult.Failed -> {
                    Log.e(TAG, "安装失败: ${installResult.message}")
                    _updateState.value = UpdateState.Error("安装失败: ${installResult.message}")
                }
                is InstallResult.PermissionDenied -> {
                    Log.w(TAG, "安装权限被拒绝")
                    _updateState.value = UpdateState.Error("需要安装权限")
                }
                is InstallResult.UserCancelled -> {
                    Log.i(TAG, "用户取消安装")
                    _updateState.value = UpdateState.Cancelled
                }
            }
            
            installResult
            
        } catch (e: Exception) {
            val errorMsg = "安装异常: ${e.message}"
            Log.e(TAG, errorMsg, e)
            _updateState.value = UpdateState.Error(errorMsg)
            InstallResult.Failed(errorMsg)
        }
    }
    
    /**
     * 取消更新
     */
    fun cancelUpdate() {
        downloadManager.cancelDownload()
        _updateState.value = UpdateState.Cancelled
        Log.i(TAG, "更新已取消")
    }
    
    /**
     * 重置更新状态
     */
    fun resetState() {
        downloadManager.resetDownloadState()
        _updateState.value = UpdateState.Idle
        currentUpdateInfo = null
        Log.d(TAG, "更新状态已重置")
    }
    
    /**
     * 获取下载进度
     */
    fun getDownloadProgress(): StateFlow<DownloadProgress> {
        return downloadManager.downloadProgress
    }
    
    /**
     * 检查是否有安装权限
     */
    fun hasInstallPermission(): Boolean {
        return ApkInstaller.hasInstallPermission(context)
    }
    
    /**
     * 清理更新缓存
     */
    fun clearUpdateCache() {
        UpdateUtils.clearUpdateCache(context)
        Log.d(TAG, "更新缓存已清理")
    }
    
    /**
     * 获取可用存储空间
     */
    private fun getAvailableStorageSpace(): Long {
        return try {
            val cacheDir = UpdateUtils.getUpdateCacheDir(context)
            cacheDir.usableSpace
        } catch (e: Exception) {
            Log.e(TAG, "获取存储空间失败", e)
            0L
        }
    }
    
    /**
     * 获取网络状态描述
     */
    fun getNetworkStatus(): String {
        return UpdateUtils.getNetworkTypeDescription(context)
    }
    
    /**
     * 是否为WiFi网络
     */
    fun isWifiConnected(): Boolean {
        return updateRepository.isWifiConnected()
    }
}

/**
 * 更新状态密封类
 */
sealed class UpdateState {
    object Idle : UpdateState()
    object Checking : UpdateState()
    object NoUpdate : UpdateState()
    data class UpdateAvailable(val updateInfo: UpdateInfo) : UpdateState()
    data class Downloading(val updateInfo: UpdateInfo) : UpdateState()
    data class DownloadCompleted(val updateInfo: UpdateInfo, val apkFile: File) : UpdateState()
    data class Installing(val updateInfo: UpdateInfo, val apkFile: File) : UpdateState()
    data class InstallStarted(val updateInfo: UpdateInfo, val apkFile: File) : UpdateState()
    data class Error(val message: String) : UpdateState()
    object Cancelled : UpdateState()
}
