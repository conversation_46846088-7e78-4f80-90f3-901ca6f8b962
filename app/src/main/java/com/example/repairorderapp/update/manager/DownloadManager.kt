package com.example.repairorderapp.update.manager

import android.content.Context
import android.util.Log
import com.example.repairorderapp.network.ApiClient
import com.example.repairorderapp.update.model.DownloadProgress
import com.example.repairorderapp.update.model.DownloadStatus
import com.example.repairorderapp.update.model.UpdateInfo
import com.example.repairorderapp.update.utils.UpdateUtils
import com.example.repairorderapp.update.utils.ZipExtractor
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.ResponseBody
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * 下载管理器
 * 负责APK文件的下载，支持断点续传和进度监控
 */
class DownloadManager(private val context: Context) {
    
    companion object {
        private const val TAG = "DownloadManager"
        private const val BUFFER_SIZE = 8192
        private const val CONNECT_TIMEOUT = 30L
        private const val READ_TIMEOUT = 60L

        // 保存待清理的文件信息
        private var pendingZipFile: File? = null
        private var pendingExtractDir: File? = null
    }
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
        .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
        .build()
    
    private var downloadJob: Job? = null
    
    private val _downloadProgress = MutableStateFlow(
        DownloadProgress(DownloadStatus.IDLE)
    )
    val downloadProgress: StateFlow<DownloadProgress> = _downloadProgress.asStateFlow()
    
    /**
     * 开始下载APK
     * @param updateInfo 更新信息
     * @return 下载的文件，如果失败返回null
     */
    suspend fun downloadApk(updateInfo: UpdateInfo): File? = withContext(Dispatchers.IO) {
        try {
            Log.i(TAG, "开始下载APK: ${updateInfo.versionName}")
            
            // 创建下载目录和文件
            val downloadDir = UpdateUtils.getUpdateCacheDir(context)

            // 根据下载URL确定文件扩展名
            val fileExtension = if (updateInfo.downloadUrl.lowercase().endsWith(".zip")) {
                ".zip"
            } else {
                ".apk"
            }
            val downloadFile = File(downloadDir, "app-${updateInfo.versionName}$fileExtension")
            
            // 如果文件已存在且校验通过，直接返回
            if (downloadFile.exists() && UpdateUtils.verifyFile(downloadFile, updateInfo.fileMd5)) {
                Log.i(TAG, "文件已存在且校验通过，跳过下载")
                _downloadProgress.value = DownloadProgress(
                    status = DownloadStatus.COMPLETED,
                    progress = 100,
                    downloadedBytes = downloadFile.length(),
                    totalBytes = updateInfo.fileSize
                )
                return@withContext downloadFile
            }

            // 删除已存在的无效文件
            if (downloadFile.exists()) {
                downloadFile.delete()
            }
            
            // 开始下载
            _downloadProgress.value = DownloadProgress(DownloadStatus.DOWNLOADING)

            // 处理下载URL，支持相对路径和绝对路径
            val downloadUrl = buildDownloadUrl(updateInfo.downloadUrl)

            val request = Request.Builder()
                .url(downloadUrl)
                .build()
            
            val response = okHttpClient.newCall(request).execute()

            if (!response.isSuccessful) {
                val errorMsg = "下载失败: HTTP ${response.code} - ${response.message}"
                Log.e(TAG, "$errorMsg, URL: $downloadUrl")
                _downloadProgress.value = DownloadProgress(
                    status = DownloadStatus.FAILED,
                    errorMessage = errorMsg
                )
                response.close() // 关闭响应以避免连接泄漏
                return@withContext null
            }

            val responseBody = response.body
            if (responseBody == null) {
                val errorMsg = "响应体为空"
                Log.e(TAG, errorMsg)
                _downloadProgress.value = DownloadProgress(
                    status = DownloadStatus.FAILED,
                    errorMessage = errorMsg
                )
                response.close() // 关闭响应以避免连接泄漏
                return@withContext null
            }
            
            // 执行下载
            val downloadedFile = downloadWithProgress(responseBody, downloadFile, updateInfo.fileSize)
            
            if (downloadedFile != null) {
                // 验证下载的文件
                if (UpdateUtils.verifyFile(downloadedFile, updateInfo.fileMd5)) {
                    Log.i(TAG, "文件下载完成并验证成功")

                    // 检查是否为ZIP文件，如果是则解压提取APK
                    val finalApkFile = if (ZipExtractor.isZipFile(downloadedFile)) {
                        Log.i(TAG, "检测到ZIP文件，开始解压")
                        extractApkFromZip(downloadedFile, updateInfo)
                    } else {
                        Log.i(TAG, "直接APK文件，无需解压")
                        downloadedFile
                    }

                    if (finalApkFile != null) {
                        _downloadProgress.value = DownloadProgress(
                            status = DownloadStatus.COMPLETED,
                            progress = 100,
                            downloadedBytes = finalApkFile.length(),
                            totalBytes = updateInfo.fileSize
                        )
                        return@withContext finalApkFile
                    } else {
                        _downloadProgress.value = DownloadProgress(
                            status = DownloadStatus.FAILED,
                            errorMessage = "ZIP解压失败"
                        )
                        return@withContext null
                    }
                } else {
                    Log.e(TAG, "文件校验失败，删除下载文件")
                    downloadedFile.delete()
                    _downloadProgress.value = DownloadProgress(
                        status = DownloadStatus.FAILED,
                        errorMessage = "文件校验失败"
                    )
                    return@withContext null
                }
            } else {
                _downloadProgress.value = DownloadProgress(
                    status = DownloadStatus.FAILED,
                    errorMessage = "下载失败"
                )
                return@withContext null
            }
            
        } catch (e: CancellationException) {
            Log.i(TAG, "下载被取消")
            _downloadProgress.value = DownloadProgress(DownloadStatus.CANCELLED)
            throw e
        } catch (e: Exception) {
            val errorMsg = when {
                e.message?.contains("DownloadForbidden") == true ->
                    "COS域名限制：不允许使用默认域名下载ZIP/APK文件"
                e.message?.contains("403") == true ->
                    "访问被拒绝：可能是COS权限或域名限制问题"
                e.message?.contains("404") == true ->
                    "文件不存在：请检查下载URL是否正确"
                e.message?.contains("timeout") == true ->
                    "下载超时：请检查网络连接"
                else -> "下载异常: ${e.message}"
            }

            Log.e(TAG, "下载异常详情: $errorMsg", e)
            _downloadProgress.value = DownloadProgress(
                status = DownloadStatus.FAILED,
                errorMessage = errorMsg
            )
            return@withContext null
        }
    }
    
    /**
     * 带进度的下载实现
     */
    private suspend fun downloadWithProgress(
        responseBody: ResponseBody,
        targetFile: File,
        expectedSize: Long
    ): File? = withContext(Dispatchers.IO) {
        var inputStream: InputStream? = null
        var outputStream: FileOutputStream? = null
        
        try {
            inputStream = responseBody.byteStream()
            outputStream = FileOutputStream(targetFile)
            
            val buffer = ByteArray(BUFFER_SIZE)
            var downloadedBytes = 0L
            val totalBytes = responseBody.contentLength().takeIf { it > 0 } ?: expectedSize
            var lastUpdateTime = System.currentTimeMillis()
            var lastDownloadedBytes = 0L
            
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                // 检查是否被取消
                ensureActive()
                
                outputStream.write(buffer, 0, bytesRead)
                downloadedBytes += bytesRead
                
                // 更新进度（每500ms更新一次）
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastUpdateTime >= 500) {
                    val progress = if (totalBytes > 0) {
                        ((downloadedBytes * 100) / totalBytes).toInt()
                    } else {
                        0
                    }
                    
                    // 计算下载速度
                    val timeDiff = currentTime - lastUpdateTime
                    val bytesDiff = downloadedBytes - lastDownloadedBytes
                    val speed = if (timeDiff > 0) {
                        (bytesDiff * 1000) / timeDiff
                    } else {
                        0L
                    }
                    
                    // 计算剩余时间
                    val remainingBytes = totalBytes - downloadedBytes
                    val remainingTime = if (speed > 0) {
                        remainingBytes / speed
                    } else {
                        0L
                    }
                    
                    _downloadProgress.value = DownloadProgress(
                        status = DownloadStatus.DOWNLOADING,
                        progress = progress,
                        downloadedBytes = downloadedBytes,
                        totalBytes = totalBytes,
                        speed = UpdateUtils.formatSpeed(speed),
                        remainingTime = UpdateUtils.formatRemainingTime(remainingTime)
                    )
                    
                    lastUpdateTime = currentTime
                    lastDownloadedBytes = downloadedBytes
                }
            }
            
            outputStream.flush()
            Log.i(TAG, "文件下载完成: ${targetFile.name}, 大小: ${UpdateUtils.formatBytes(downloadedBytes)}")
            
            return@withContext targetFile
            
        } catch (e: Exception) {
            Log.e(TAG, "下载过程中发生异常", e)
            // 删除不完整的文件
            if (targetFile.exists()) {
                targetFile.delete()
            }
            return@withContext null
        } finally {
            try {
                inputStream?.close()
                outputStream?.close()
                responseBody.close()
            } catch (e: Exception) {
                Log.w(TAG, "关闭流时发生异常", e)
            }
        }
    }
    
    /**
     * 取消下载
     */
    fun cancelDownload() {
        downloadJob?.cancel()
        _downloadProgress.value = DownloadProgress(DownloadStatus.CANCELLED)
        Log.i(TAG, "下载已取消")
    }
    
    /**
     * 重置下载状态
     */
    fun resetDownloadState() {
        _downloadProgress.value = DownloadProgress(DownloadStatus.IDLE)
    }
    
    /**
     * 检查是否正在下载
     */
    fun isDownloading(): Boolean {
        return _downloadProgress.value.status == DownloadStatus.DOWNLOADING
    }

    /**
     * 构建下载URL，处理相对路径和绝对路径
     * @param url 原始URL
     * @return 完整的下载URL
     */
    private fun buildDownloadUrl(url: String): String {
        return when {
            // 如果已经是完整的URL（包含http或https），直接返回
            url.startsWith("http://") || url.startsWith("https://") -> {
                Log.d(TAG, "使用完整URL: $url")
                url
            }
            // 如果是相对路径，拼接基础URL
            url.startsWith("/") -> {
                val baseUrl = ApiClient.BASE_URL.trimEnd('/')
                val fullUrl = "$baseUrl$url"
                Log.d(TAG, "拼接相对路径: $baseUrl + $url = $fullUrl")
                fullUrl
            }
            // 其他情况，也当作相对路径处理
            else -> {
                val baseUrl = ApiClient.BASE_URL.trimEnd('/')
                val fullUrl = "$baseUrl/$url"
                Log.d(TAG, "拼接路径: $baseUrl + /$url = $fullUrl")
                fullUrl
            }
        }
    }

    /**
     * 从ZIP文件中解压提取APK
     * @param zipFile ZIP文件
     * @param updateInfo 更新信息
     * @return 提取的APK文件，失败返回null
     */
    private suspend fun extractApkFromZip(zipFile: File, updateInfo: UpdateInfo): File? {
        return try {
            Log.i(TAG, "开始从ZIP文件解压APK: ${zipFile.name}")

            // 创建解压目录
            val extractDir = File(zipFile.parent, "extract_${System.currentTimeMillis()}")

            // 设置解压进度回调
            val progressCallback = object : ZipExtractor.ProgressCallback {
                override fun onProgress(progress: Int, currentFile: String) {
                    // 解压进度映射到80-95%
                    val mappedProgress = 80 + (progress * 15) / 100
                    _downloadProgress.value = DownloadProgress(
                        status = DownloadStatus.EXTRACTING,
                        progress = mappedProgress,
                        downloadedBytes = zipFile.length(),
                        totalBytes = updateInfo.fileSize,
                        speed = "解压中...",
                        remainingTime = "正在解压: $currentFile"
                    )
                }

                override fun onError(message: String) {
                    Log.e(TAG, "解压过程中出错: $message")
                }
            }

            // 执行解压
            when (val result = ZipExtractor.extractApkFromZip(zipFile, extractDir, progressCallback)) {
                is ZipExtractor.ExtractResult.Success -> {
                    Log.i(TAG, "ZIP解压成功，APK文件: ${result.apkFile.name}")

                    // 将APK文件复制到最终位置
                    val finalApkFile = File(zipFile.parent, "app-${updateInfo.versionName}.apk")

                    // 使用复制而不是移动，更安全可靠
                    try {
                        result.apkFile.copyTo(finalApkFile, overwrite = true)
                        Log.i(TAG, "APK文件复制成功: ${finalApkFile.name}")

                        // 验证复制结果
                        if (finalApkFile.exists() && finalApkFile.length() == result.apkFile.length()) {
                            // 保存清理信息，供安装完成后使用
                            saveCleanupInfo(zipFile, extractDir)

                            Log.i(TAG, "APK文件准备完成，等待安装完成后清理临时文件")
                            return@extractApkFromZip finalApkFile
                        } else {
                            Log.e(TAG, "文件复制验证失败: 目标文件不存在或大小不匹配")
                            finalApkFile.delete()
                            ZipExtractor.cleanupExtractedFiles(extractDir)
                            return@extractApkFromZip null
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "APK文件复制失败", e)
                        ZipExtractor.cleanupExtractedFiles(extractDir)
                        return@extractApkFromZip null
                    }
                }
                is ZipExtractor.ExtractResult.Failed -> {
                    Log.e(TAG, "ZIP解压失败: ${result.message}")
                    ZipExtractor.cleanupExtractedFiles(extractDir)
                    return@extractApkFromZip null
                }
                is ZipExtractor.ExtractResult.NoApkFound -> {
                    Log.e(TAG, "ZIP文件中未找到APK文件")
                    ZipExtractor.cleanupExtractedFiles(extractDir)
                    return@extractApkFromZip null
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "解压ZIP文件异常", e)
            return@extractApkFromZip null
        }
    }

    /**
     * 保存清理信息，供安装完成后使用
     */
    private fun saveCleanupInfo(zipFile: File, extractDir: File) {
        pendingZipFile = zipFile
        pendingExtractDir = extractDir
    }

    /**
     * 执行延迟清理（在安装完成后调用）
     */
    fun performDelayedCleanup() {
        try {
            pendingZipFile?.let { zipFile ->
                if (zipFile.exists()) {
                    zipFile.delete()
                }
            }

            pendingExtractDir?.let { extractDir ->
                ZipExtractor.cleanupExtractedFiles(extractDir)
            }

            // 清空待清理信息
            pendingZipFile = null
            pendingExtractDir = null

        } catch (e: Exception) {
            Log.e(TAG, "延迟清理失败", e)
        }
    }

}
