package com.example.repairorderapp.update.utils

import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import java.io.File
import java.security.MessageDigest

/**
 * 文件校验工具类
 * 负责APK文件的完整性和安全性验证
 */
object FileVerifier {
    
    private const val TAG = "FileVerifier"
    
    /**
     * 验证APK文件的完整性和有效性
     * @param context 上下文
     * @param apkFile APK文件
     * @param expectedMd5 期望的MD5值，null表示跳过MD5校验
     * @return 验证结果
     */
    fun verifyApkFile(context: Context, apkFile: File, expectedMd5: String?): VerificationResult {
        try {
            // 1. 检查文件是否存在
            if (!apkFile.exists()) {
                return VerificationResult.Failed("APK文件不存在")
            }
            
            // 2. 检查文件大小
            if (apkFile.length() == 0L) {
                return VerificationResult.Failed("APK文件为空")
            }
            
            // 3. 验证MD5（可选）
            val actualMd5 = if (expectedMd5 != null) {
                val md5 = calculateMD5(apkFile)
                if (md5 == null) {
                    return VerificationResult.Failed("无法计算文件MD5")
                }

                if (!md5.equals(expectedMd5, ignoreCase = true)) {
                    Log.w(TAG, "MD5校验失败 - 期望: $expectedMd5, 实际: $md5")
                    return VerificationResult.Failed("文件完整性校验失败")
                }
                md5
            } else {
                "跳过校验"
            }
            
            // 4. 验证APK包信息
            val packageInfo = try {
                context.packageManager.getPackageArchiveInfo(
                    apkFile.absolutePath,
                    PackageManager.GET_ACTIVITIES
                )
            } catch (e: Exception) {
                Log.e(TAG, "解析APK包信息失败", e)
                return VerificationResult.Failed("APK文件格式无效")
            }
            
            if (packageInfo == null) {
                return VerificationResult.Failed("无法解析APK包信息")
            }
            
            // 5. 验证包名是否匹配
            val currentPackageName = context.packageName
            if (packageInfo.packageName != currentPackageName) {
                Log.w(TAG, "包名不匹配 - 当前: $currentPackageName, APK: ${packageInfo.packageName}")
                return VerificationResult.Failed("APK包名不匹配")
            }
            
            // 6. 验证版本号
            val currentVersionCode = try {
                context.packageManager.getPackageInfo(currentPackageName, 0).versionCode
            } catch (e: Exception) {
                Log.e(TAG, "获取当前版本号失败", e)
                return VerificationResult.Failed("无法获取当前版本信息")
            }
            
            val apkVersionCode = packageInfo.versionCode
            if (apkVersionCode <= currentVersionCode) {
                Log.w(TAG, "版本号检查 - 当前: $currentVersionCode, APK: $apkVersionCode")
                // 注意：这里不直接返回失败，因为可能是强制回退到旧版本
                // 由调用方决定是否允许安装旧版本
            }
            
            Log.i(TAG, "APK验证成功 - 包名: ${packageInfo.packageName}, 版本: ${packageInfo.versionName}(${packageInfo.versionCode})")
            
            return VerificationResult.Success(
                packageName = packageInfo.packageName,
                versionName = packageInfo.versionName ?: "未知",
                versionCode = packageInfo.versionCode,
                fileSize = apkFile.length(),
                md5 = actualMd5
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "APK验证过程中发生异常", e)
            return VerificationResult.Failed("验证过程异常: ${e.message}")
        }
    }
    
    /**
     * 计算文件MD5
     */
    private fun calculateMD5(file: File): String? {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            file.inputStream().use { inputStream ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "计算MD5失败", e)
            null
        }
    }
    
    /**
     * 快速验证文件MD5（仅校验完整性）
     */
    fun quickVerifyMD5(file: File, expectedMd5: String): Boolean {
        if (!file.exists() || file.length() == 0L) {
            return false
        }
        
        val actualMd5 = calculateMD5(file)
        return actualMd5?.equals(expectedMd5, ignoreCase = true) == true
    }
    
    /**
     * 验证结果密封类
     */
    sealed class VerificationResult {
        data class Success(
            val packageName: String,
            val versionName: String,
            val versionCode: Int,
            val fileSize: Long,
            val md5: String
        ) : VerificationResult()
        
        data class Failed(val reason: String) : VerificationResult()
    }
}
