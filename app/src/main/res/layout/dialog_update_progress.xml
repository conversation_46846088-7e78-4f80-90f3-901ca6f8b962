<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_rounded"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_progress_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="正在下载更新"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginBottom="12dp"
        android:max="100"
        android:progress="0"
        android:progressDrawable="@drawable/progress_bar_custom" />

    <!-- 进度文本 -->
    <TextView
        android:id="@+id/tv_progress_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text="0%"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- 速度和剩余时间 -->
    <TextView
        android:id="@+id/tv_progress_speed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text=""
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- 状态文本 -->
    <TextView
        android:id="@+id/tv_progress_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="准备下载..."
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- 取消按钮 -->
    <Button
        android:id="@+id/btn_progress_cancel"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/bg_button_secondary"
        android:text="取消下载"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="visible" />

</LinearLayout>
