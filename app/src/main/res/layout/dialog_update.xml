<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_rounded"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 更新图标 -->
    <ImageView
        android:id="@+id/iv_update_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_update_normal"
        android:contentDescription="更新图标" />

    <!-- 更新标题 -->
    <TextView
        android:id="@+id/tv_update_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:gravity="center"
        android:text="发现新版本"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- 版本信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_update_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="版本 1.3.0"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <View
            android:layout_width="1dp"
            android:layout_height="12dp"
            android:layout_marginHorizontal="12dp"
            android:background="@color/divider" />

        <TextView
            android:id="@+id/tv_update_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="大小: 15MB"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 更新日志 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:maxHeight="120dp">

        <TextView
            android:id="@+id/tv_update_log"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_update_log"
            android:lineSpacingExtra="4dp"
            android:padding="12dp"
            android:text="1. 修复重要问题\n2. 性能优化\n3. 新增功能"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </ScrollView>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- 稍后按钮 -->
        <Button
            android:id="@+id/btn_later"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_secondary"
            android:text="稍后提醒"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <!-- 更新按钮 -->
        <Button
            android:id="@+id/btn_update"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/bg_button_primary"
            android:text="立即更新"
            android:textColor="@android:color/white"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
